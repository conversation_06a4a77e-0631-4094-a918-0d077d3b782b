import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

/**
 * Migration script to add custom billing fields to subscription_plans table
 * Run this script to add customBillingMonths and customBillingYears columns
 */

async function addCustomBillingFields() {
  try {
    console.log('🚀 Starting migration: Adding custom billing fields...');

    // Add customBillingMonths column
    await db.execute(sql`
      ALTER TABLE subscription_plans 
      ADD COLUMN customBillingMonths INT DEFAULT 0 
      COMMENT 'Custom months for billing cycle (0 = use default)'
    `);
    console.log('✅ Added customBillingMonths column');

    // Add customBillingYears column
    await db.execute(sql`
      ALTER TABLE subscription_plans 
      ADD COLUMN customBillingYears INT DEFAULT 0 
      COMMENT 'Custom years for billing cycle (0 = use default)'
    `);
    console.log('✅ Added customBillingYears column');

    console.log('🎉 Migration completed successfully!');
    console.log('📝 Custom billing fields have been added to subscription_plans table');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  addCustomBillingFields()
    .then(() => {
      console.log('✨ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { addCustomBillingFields };
