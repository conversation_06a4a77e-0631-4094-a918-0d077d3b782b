#!/usr/bin/env tsx

/**
 * Migration script to fix subscription_plans table schema
 * Adds missing columns that are defined in the schema but not in the database
 */

import mysql from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function addMissingColumns() {
  console.log('🔧 Fixing subscription_plans table schema...');
  
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });

  try {
    // Check if columns exist before adding them
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'subscription_plans'
    `, [dbConfig.database]);

    const existingColumns = (columns as any[]).map(col => col.COLUMN_NAME);
    console.log('📋 Existing columns:', existingColumns);

    const columnsToAdd = [
      {
        name: 'canCreateFanPages',
        sql: 'ADD COLUMN canCreateFanPages BOOLEAN DEFAULT FALSE'
      },
      {
        name: 'canCreateStores', 
        sql: 'ADD COLUMN canCreateStores BOOLEAN DEFAULT FALSE'
      },
      {
        name: 'canMonetizeBlogs',
        sql: 'ADD COLUMN canMonetizeBlogs BOOLEAN DEFAULT FALSE'
      },
      {
        name: 'prioritySupport',
        sql: 'ADD COLUMN prioritySupport BOOLEAN DEFAULT FALSE'
      },
      {
        name: 'grantsVerification',
        sql: 'ADD COLUMN grantsVerification BOOLEAN DEFAULT FALSE'
      },
      {
        name: 'badgeType',
        sql: "ADD COLUMN badgeType ENUM('none', 'crown', 'star', 'diamond', 'vip', 'custom') DEFAULT 'none' NOT NULL"
      },
      {
        name: 'badgeColor',
        sql: "ADD COLUMN badgeColor VARCHAR(7) DEFAULT '#3B82F6'"
      },
      {
        name: 'customBadgeUrl',
        sql: 'ADD COLUMN customBadgeUrl VARCHAR(500)'
      },
      {
        name: 'badgePriority',
        sql: 'ADD COLUMN badgePriority INT DEFAULT 0'
      }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding column: ${column.name}`);
        await connection.execute(`ALTER TABLE subscription_plans ${column.sql}`);
        console.log(`✅ Added column: ${column.name}`);
      } else {
        console.log(`⏭️  Column ${column.name} already exists, skipping`);
      }
    }

    console.log('🎉 Schema migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function verifySchema() {
  console.log('\n🔍 Verifying schema after migration...');
  
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });

  try {
    // Test the query that was failing
    const [result] = await connection.execute(`
      SELECT id, name, displayName, description, price, currency, billingCycle, 
             features, maxPosts, maxStorage, maxGroups, canCreateFanPages, 
             canCreateStores, canMonetizeBlogs, prioritySupport, grantsVerification, 
             badgeType, badgeColor, customBadgeUrl, badgePriority, isActive, 
             sortOrder, createdAt, updatedAt 
      FROM subscription_plans 
      LIMIT 1
    `);
    
    console.log('✅ Schema verification successful!');
    console.log(`📊 Query returned ${(result as any[]).length} row(s)`);
    
  } catch (error) {
    console.error('❌ Schema verification failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function main() {
  console.log('🚀 Starting subscription_plans schema migration...\n');
  
  try {
    await addMissingColumns();
    await verifySchema();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('💡 You can now restart your application and the API endpoints should work.');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
main().catch((error) => {
  console.error('💥 Migration script failed:', error);
  process.exit(1);
});
