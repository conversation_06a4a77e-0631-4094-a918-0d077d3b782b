import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users, fanPagePosts, fanPages, fanPageFollowers, fanPagePostLikes, subscriptions, blogs, blogLikes, blogBookmarks, groups, groupMembers, savedPosts } from "@/lib/db/schema";
import { desc, eq, and, inArray, or, sql } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const currentUserId = session.user.id;

    // Get pagination parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Max 20 posts per page
    const offset = (page - 1) * limit;

    // Get user's subscriptions to filter content
    const userSubscriptions = await db.query.subscriptions.findMany({
      where: eq(subscriptions.subscriberId, currentUserId),
      columns: {
        targetUserId: true,
      },
    });

    const subscribedUserIds = userSubscriptions.map(sub => sub.targetUserId);

    // Get followed fan pages
    const followedPages = await db
      .select({ fanPageId: fanPageFollowers.fanPageId })
      .from(fanPageFollowers)
      .where(eq(fanPageFollowers.userId, currentUserId));

    const followedPageIds = followedPages.map(fp => fp.fanPageId);

    // Get user's group memberships
    const userGroups = await db
      .select({ groupId: groupMembers.groupId })
      .from(groupMembers)
      .where(
        and(
          eq(groupMembers.userId, currentUserId),
          or(
            eq(groupMembers.role, 'member'),
            eq(groupMembers.role, 'admin'),
            eq(groupMembers.role, 'moderator')
          )
        )
      );

    const memberGroupIds = userGroups.map(ug => ug.groupId);

    // Fetch user posts with privacy filtering and pagination (excluding group posts)
    const userPosts = await db.query.posts.findMany({
      where: and(
        // Exclude group posts (groupId should be null)
        sql`${posts.groupId} IS NULL`,
        // Only show published posts
        eq(posts.isPublished, true),
        or(
          // Public posts from anyone
          eq(posts.privacy, 'public'),
          // User's own posts (all privacy levels)
          eq(posts.userId, currentUserId),
          // Subscribers-only posts from users the current user is subscribed to
          ...(subscribedUserIds.length > 0 ? [
            and(
              eq(posts.privacy, 'subscribers'),
              sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
            )
          ] : [])
        )
      ),
      orderBy: [desc(posts.createdAt)],
      limit: Math.ceil(limit / 4), // Split between user posts, fan page posts, group posts, and blog posts
      offset: Math.floor(offset / 4),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    // Fetch fan page posts from followed pages
    let fanPagePostsData: any[] = [];
    if (followedPageIds.length > 0) {
      fanPagePostsData = await db
        .select({
          id: fanPagePosts.id,
          content: fanPagePosts.content,
          images: fanPagePosts.images,
          videos: fanPagePosts.videos,
          type: fanPagePosts.type,
          likeCount: fanPagePosts.likeCount,
          commentCount: fanPagePosts.commentCount,
          shareCount: fanPagePosts.shareCount,
          viewCount: fanPagePosts.viewCount,
          createdAt: fanPagePosts.createdAt,
          fanPage: {
            id: fanPages.id,
            name: fanPages.name,
            username: fanPages.username,
            profileImage: fanPages.profileImage,
            isVerified: fanPages.isVerified,
          },
        })
        .from(fanPagePosts)
        .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
        .where(
          and(
            inArray(fanPagePosts.fanPageId, followedPageIds),
            eq(fanPagePosts.isPublished, true)
          )
        )
        .orderBy(desc(fanPagePosts.createdAt))
        .limit(Math.floor(limit / 4))
        .offset(Math.ceil(offset / 4));
    }

    // Fetch group posts from user's joined groups
    let groupPostsData: any[] = [];
    if (memberGroupIds.length > 0) {
      groupPostsData = await db.query.posts.findMany({
        where: and(
          inArray(posts.groupId, memberGroupIds),
          eq(posts.privacy, 'public'), // Group posts are typically public within the group
          eq(posts.isPublished, true) // Only show published posts
        ),
        orderBy: [desc(posts.createdAt)],
        limit: Math.floor(limit / 4), // Divide by 4 now since we have 4 types of posts
        offset: Math.ceil(offset / 4),
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              username: true,
              image: true,
            },
          },
          group: {
            columns: {
              id: true,
              name: true,
              coverImage: true,
              visibility: true,
            },
          },
          likes: true,
          comments: {
            columns: {
              id: true,
            },
          },
          sharedPost: {
            with: {
              user: {
                columns: {
                  id: true,
                  name: true,
                  username: true,
                  image: true,
                },
              },
            },
          },
          shares: true,
        },
      });
    }

    // Fetch published blog posts
    const blogPosts = await db.query.blogs.findMany({
      where: eq(blogs.status, 'published'),
      orderBy: [desc(blogs.publishedAt)],
      limit: Math.floor(limit / 4),
      offset: Math.ceil(offset / 4),
      with: {
        author: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        category: true,
        likes: true,
        dislikes: true,
        comments: true,
        bookmarks: true,
      },
    });

    // Transform user posts to unified format
    const transformedUserPosts = userPosts.map(post => ({
      id: post.id,
      content: post.content,
      images: post.images,
      videos: post.videos,
      privacy: post.privacy,
      backgroundColor: post.backgroundColor,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      formattedContent: post.formattedContent,
      createdAt: post.createdAt,
      type: 'user_post' as const,
      user: post.user,
      fanPage: null,
      _count: {
        likes: post.likes?.length || 0,
        dislikes: 0, // User posts don't have dislikes in the current schema
        comments: post.comments?.length || 0,
        shares: post.shares?.length || 0,
      },
      liked: post.likes?.some(like => like.userId === currentUserId) || false,
      disliked: false,
      sharedPost: post.sharedPost,
    }));

    // Get fan page post likes/dislikes for current user
    let fanPagePostLikesData: any[] = [];
    if (fanPagePostsData.length > 0) {
      const fanPagePostIds = fanPagePostsData.map(post => post.id);
      fanPagePostLikesData = await db
        .select({
          postId: fanPagePostLikes.fanPagePostId,
          type: fanPagePostLikes.type,
        })
        .from(fanPagePostLikes)
        .where(
          and(
            inArray(fanPagePostLikes.fanPagePostId, fanPagePostIds),
            eq(fanPagePostLikes.userId, currentUserId)
          )
        );
    }

    // Transform fan page posts to unified format
    const transformedFanPagePosts = fanPagePostsData.map(post => {
      const userLike = fanPagePostLikesData.find(like => like.postId === post.id && like.type === 'like');
      const userDislike = fanPagePostLikesData.find(like => like.postId === post.id && like.type === 'angry');

      return {
        id: post.id,
        content: post.content,
        images: post.images,
        videos: post.videos,
        privacy: 'public', // Fan page posts are always public
        backgroundColor: null,
        feeling: null,
        activity: null,
        location: null,
        formattedContent: null,
        createdAt: post.createdAt,
        type: 'fan_page_post' as const,
        user: null,
        fanPage: post.fanPage,
        _count: {
          likes: post.likeCount || 0,
          dislikes: 0, // We don't track dislike count separately for fan pages
          comments: post.commentCount || 0,
          shares: post.shareCount || 0,
        },
        liked: !!userLike,
        disliked: !!userDislike,
        sharedPost: null,
      };
    });

    // Transform group posts to unified format
    const transformedGroupPosts = groupPostsData.map(post => ({
      id: post.id,
      content: post.content,
      images: post.images,
      videos: post.videos,
      privacy: post.privacy,
      backgroundColor: post.backgroundColor,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      formattedContent: post.formattedContent,
      createdAt: post.createdAt,
      type: 'group_post' as const,
      user: post.user,
      fanPage: null,
      group: {
        id: post.group?.id || '',
        name: post.group?.name || '',
        slug: post.group?.id || '', // Using id as slug since groups table doesn't have slug field
        profileImage: post.group?.coverImage || null,
        isPrivate: post.group?.visibility !== 'public',
      },
      _count: {
        likes: post.likes?.length || 0,
        dislikes: 0, // Group posts don't have dislikes in the current schema
        comments: post.comments?.length || 0,
        shares: post.shares?.length || 0,
      },
      liked: post.likes?.some(like => like.userId === currentUserId) || false,
      disliked: false,
      sharedPost: post.sharedPost,
    }));

    // Transform blog posts to unified format
    const transformedBlogPosts = blogPosts.map(blog => ({
      id: blog.id,
      content: null, // We'll use blog.excerpt in the blog field instead
      images: blog.coverImage ? [blog.coverImage] : null,
      videos: null,
      privacy: 'public', // Blog posts are always public
      backgroundColor: null,
      feeling: null,
      activity: null,
      location: null,
      formattedContent: null,
      createdAt: blog.publishedAt || blog.createdAt,
      type: 'blog_post' as const,
      user: blog.author,
      fanPage: null,
      group: null,
      blog: {
        id: blog.id,
        title: blog.title,
        slug: blog.slug,
        excerpt: blog.excerpt,
        coverImage: blog.coverImage,
        readTime: blog.readTime,
        viewCount: blog.viewCount,
        category: blog.category,
        tags: blog.tags,
        featured: blog.featured,
      },
      _count: {
        likes: blog.likes?.length || 0,
        dislikes: blog.dislikes?.length || 0,
        comments: blog.comments?.length || 0,
        shares: 0, // We'll add blog shares later if needed
      },
      liked: blog.likes?.some(like => like.userId === currentUserId) || false,
      disliked: blog.dislikes?.some(dislike => dislike.userId === currentUserId) || false,
      sharedPost: null,
    }));

    // Combine and sort all posts by creation date
    const allPosts = [...transformedUserPosts, ...transformedFanPagePosts, ...transformedGroupPosts, ...transformedBlogPosts]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Get bookmark status for all posts
    const postIds = allPosts.map(post => post.id);
    const bookmarkedPosts = postIds.length > 0 ? await db
      .select({ postId: savedPosts.postId })
      .from(savedPosts)
      .where(
        and(
          eq(savedPosts.userId, currentUserId),
          inArray(savedPosts.postId, postIds)
        )
      ) : [];

    const bookmarkedPostIds = new Set(bookmarkedPosts.map(bp => bp.postId));

    // Add bookmark status to all posts
    const postsWithBookmarks = allPosts.map(post => ({
      ...post,
      isBookmarked: bookmarkedPostIds.has(post.id),
    }));

    // For total calculation, we need to count all available posts with privacy filtering (excluding group posts)
    const totalUserPostsCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(and(
        // Exclude group posts (groupId should be null)
        sql`${posts.groupId} IS NULL`,
        or(
          eq(posts.privacy, 'public'),
          eq(posts.userId, currentUserId),
          ...(subscribedUserIds.length > 0 ? [
            and(
              eq(posts.privacy, 'subscribers'),
              sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
            )
          ] : [])
        )
      ));

    const totalFanPagePostsCount = followedPageIds.length > 0 ? await db
      .select({ count: sql<number>`count(*)` })
      .from(fanPagePosts)
      .where(
        and(
          inArray(fanPagePosts.fanPageId, followedPageIds),
          eq(fanPagePosts.isPublished, true)
        )
      ) : [{ count: 0 }];

    const totalGroupPostsCount = memberGroupIds.length > 0 ? await db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(
        and(
          inArray(posts.groupId, memberGroupIds),
          eq(posts.privacy, 'public')
        )
      ) : [{ count: 0 }];

    const totalBlogPostsCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(blogs)
      .where(eq(blogs.status, 'published'));

    const totalCount = (totalUserPostsCount[0]?.count || 0) + (totalFanPagePostsCount[0]?.count || 0) + (totalGroupPostsCount[0]?.count || 0) + (totalBlogPostsCount[0]?.count || 0);
    const totalPages = Math.ceil(totalCount / limit);

    // Calculate pagination metadata
    // hasMore should be true if there are more posts available beyond the current page
    const hasMore = (offset + postsWithBookmarks.length) < totalCount;

    return NextResponse.json({
      data: postsWithBookmarks,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
        totalPages,
        currentCount: postsWithBookmarks.length,
      },
    });

  } catch (error) {
    console.error("Error fetching feed:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
