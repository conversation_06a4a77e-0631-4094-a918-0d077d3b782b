import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, subscriptions } from "@/lib/db/schema";
import { eq, and, or, desc } from "drizzle-orm";

export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const userId = params.userId;

    // Check if the current user can view private posts (own profile)
    const canViewPrivatePosts = userId === session.user.id;
    let canViewSubscriberPosts = userId === session.user.id;

    // Check subscription status if not viewing own profile
    if (!canViewPrivatePosts) {
      const subscription = await db.query.subscriptions.findFirst({
        where: and(
          eq(subscriptions.subscriberId, session.user.id),
          eq(subscriptions.targetUserId, userId)
        ),
      });

      canViewSubscriberPosts = !!subscription;
    }

    // Build the privacy conditions
    const privacyConditions = [eq(posts.privacy, "public")];

    if (canViewSubscriberPosts) {
      privacyConditions.push(eq(posts.privacy, "subscribers"));
    }

    if (canViewPrivatePosts) {
      privacyConditions.push(eq(posts.privacy, "private"));
    }

    // Build publication status conditions
    const publicationConditions = [];

    // Always include published posts
    publicationConditions.push(eq(posts.isPublished, true));

    // Include unpublished (scheduled) posts only if viewing own profile
    if (canViewPrivatePosts) {
      publicationConditions.push(eq(posts.isPublished, false));
    }

    // Determine which posts to fetch based on privacy settings
    const userPosts = await db.query.posts.findMany({
      where: and(
        eq(posts.userId, userId),
        or(...privacyConditions),
        or(...publicationConditions)
      ),
      orderBy: [desc(posts.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    // Format posts for the frontend
    const formattedPosts = await Promise.all(
      userPosts.map(async (post) => {
        // Check if the current user has liked or disliked this post
        const userLike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'like'
        );

        const userDislike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'dislike'
        );

        // Count likes and dislikes
        const likesCount = post.likes.filter(like => like.type === 'like').length;
        const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

        return {
          id: post.id,
          content: post.content,
          images: post.images,
          videos: post.videos,
          privacy: post.privacy,
          backgroundColor: post.backgroundColor,
          feeling: post.feeling,
          activity: post.activity,
          location: post.location,
          formattedContent: post.formattedContent,
          createdAt: post.createdAt.toISOString(),
          user: post.user,
          _count: {
            likes: likesCount,
            dislikes: dislikesCount,
            comments: post.comments.length,
            shares: post.shares.length,
          },
          liked: !!userLike,
          disliked: !!userDislike,
          sharedPost: post.sharedPost ? {
            id: post.sharedPost.id,
            content: post.sharedPost.content,
            images: post.sharedPost.images,
            videos: post.sharedPost.videos,
            backgroundColor: post.sharedPost.backgroundColor,
            feeling: post.sharedPost.feeling,
            activity: post.sharedPost.activity,
            location: post.sharedPost.location,
            formattedContent: post.sharedPost.formattedContent,
            createdAt: post.sharedPost.createdAt.toISOString(),
            user: post.sharedPost.user,
          } : null,
        };
      })
    );

    return NextResponse.json(formattedPosts);
  } catch (error) {
    console.error("Error fetching user posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
