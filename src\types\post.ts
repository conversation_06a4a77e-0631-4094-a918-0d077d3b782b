// Unified Post type definitions for the application

export interface BaseUser {
  id: string;
  name: string;
  username?: string | null;
  image: string | null;
}

export interface FanPage {
  id: string;
  name: string;
  username: string;
  profileImage: string | null;
  isVerified: boolean;
}

export interface Group {
  id: string;
  name: string;
  slug: string;
  profileImage: string | null;
  isPrivate: boolean;
}

export interface PostCounts {
  likes: number;
  dislikes: number;
  comments: number;
  shares: number;
}

export interface SharedPost {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  user: BaseUser;
}

// Blog interface for blog-specific data
export interface BlogData {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  coverImage?: string | null;
  readTime?: number | null;
  viewCount?: number;
  category?: {
    id: string;
    name: string;
    color: string;
  } | null;
  tags?: string[] | null;
  featured?: boolean;
}

// Main Post interface that all components should use
export interface Post {
  id: string;
  content: string | null;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  scheduledAt: string | null;
  isPublished: boolean;

  createdAt: string;
  type: 'user_post' | 'fan_page_post' | 'group_post' | 'blog_post';
  user: BaseUser | null;
  fanPage: FanPage | null;
  group: Group | null;
  blog?: BlogData | null; // Blog-specific data for blog posts
  _count: PostCounts;
  liked: boolean;
  disliked: boolean;
  isBookmarked?: boolean;
  sharedPost?: SharedPost | null;
  reported?: boolean;
  reports?: {
    id: string;
    reason: string;
    details: string;
    createdAt: string;
    user: BaseUser;
  }[];
  comments?: {
    id: string;
    content: string;
    createdAt: string;
    user: BaseUser;
  }[];
}

// Admin-specific Post type (extends base Post)
export interface AdminPost extends Post {
  user: BaseUser; // Required for admin posts
  reported?: boolean;
}

// Fan Page specific Post type
export interface FanPagePost extends Post {
  type: 'fan_page_post';
  user: null;
  fanPage: FanPage;
  group: null;
}

// Group specific Post type
export interface GroupPost extends Post {
  type: 'group_post';
  user: BaseUser | null;
  fanPage: null;
  group: Group;
}

// User specific Post type
export interface UserPost extends Post {
  type: 'user_post';
  user: BaseUser;
  fanPage: null;
  group: null;
}
