import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addMissingPostsColumns() {
  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  console.log('Connected to database');

  try {
    // Check if the columns already exist
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM posts
    `);

    const existingColumns = (columns as any[]).map(col => col.Field);
    console.log('Existing columns:', existingColumns);

    // Add missing columns one by one
    const columnsToAdd = [
      {
        name: 'groupId',
        sql: 'ADD COLUMN groupId VARCHAR(255) AFTER userId'
      },
      {
        name: 'scheduledAt',
        sql: 'ADD COLUMN scheduledAt TIMESTAMP NULL AFTER formattedContent'
      },
      {
        name: 'isPublished',
        sql: 'ADD COLUMN isPublished BOOLEAN DEFAULT TRUE AFTER scheduledAt'
      },
      {
        name: 'isReported',
        sql: 'ADD COLUMN isReported BOOLEAN DEFAULT FALSE AFTER isPublished'
      },
      {
        name: 'moderationStatus',
        sql: 'ADD COLUMN moderationStatus ENUM(\'pending\', \'approved\', \'rejected\') DEFAULT \'approved\' NOT NULL AFTER isReported'
      },
      {
        name: 'moderatedBy',
        sql: 'ADD COLUMN moderatedBy VARCHAR(255) AFTER moderationStatus'
      },
      {
        name: 'moderatedAt',
        sql: 'ADD COLUMN moderatedAt TIMESTAMP NULL AFTER moderatedBy'
      }
    ];

    // Also check if privacy enum needs updating
    const [privacyColumn] = await connection.execute(`
      SHOW COLUMNS FROM posts WHERE Field = 'privacy'
    `);

    const privacyType = (privacyColumn as any[])[0]?.Type;
    console.log('Current privacy column type:', privacyType);

    // Update privacy enum if needed
    if (privacyType && !privacyType.includes('subscribers')) {
      console.log('Updating privacy enum to include subscribers...');
      await connection.execute(`
        ALTER TABLE posts 
        MODIFY COLUMN privacy ENUM('public', 'subscribers', 'private') NOT NULL DEFAULT 'public'
      `);
      console.log('✅ Updated privacy enum');
    }

    // Add missing columns
    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`Adding ${column.name} column...`);
        await connection.execute(`ALTER TABLE posts ${column.sql}`);
        console.log(`✅ Added ${column.name} column`);
      } else {
        console.log(`ℹ️ ${column.name} column already exists`);
      }
    }

    console.log('✅ Migration completed successfully!');

  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

// Run the migration
addMissingPostsColumns().catch(console.error);
