import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment variables
config();

async function addBadgeFieldsToSubscriptionPlans() {
  let connection: mysql.Connection | null = null;

  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf_db',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Check if badge fields already exist
    console.log('Checking if badge fields already exist...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'subscription_plans'
    `, [process.env.DATABASE_NAME || 'hifnf_db']);

    const existingColumns = (columns as any[]).map(col => col.COLUMN_NAME);
    const badgeFields = ['badgeType', 'badgeColor', 'customBadgeUrl', 'badgePriority'];
    const missingFields = badgeFields.filter(field => !existingColumns.includes(field));

    if (missingFields.length === 0) {
      console.log('All badge fields already exist in subscription_plans table');
      return;
    }

    console.log(`Adding missing badge fields: ${missingFields.join(', ')}`);

    // Add badgeType field
    if (missingFields.includes('badgeType')) {
      console.log('Adding badgeType field...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN badgeType ENUM('none', 'crown', 'star', 'diamond', 'vip', 'custom') 
        DEFAULT 'none' NOT NULL
        AFTER prioritySupport
      `);
    }

    // Add badgeColor field
    if (missingFields.includes('badgeColor')) {
      console.log('Adding badgeColor field...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN badgeColor VARCHAR(7) 
        DEFAULT '#3B82F6'
        AFTER badgeType
      `);
    }

    // Add customBadgeUrl field
    if (missingFields.includes('customBadgeUrl')) {
      console.log('Adding customBadgeUrl field...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN customBadgeUrl VARCHAR(500)
        AFTER badgeColor
      `);
    }

    // Add badgePriority field
    if (missingFields.includes('badgePriority')) {
      console.log('Adding badgePriority field...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN badgePriority INT 
        DEFAULT 0
        AFTER customBadgeUrl
      `);
    }

    console.log('Badge fields added successfully to subscription_plans table');

    // Verify the changes
    console.log('Verifying changes...');
    const [updatedColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'subscription_plans'
      AND COLUMN_NAME IN ('badgeType', 'badgeColor', 'customBadgeUrl', 'badgePriority')
      ORDER BY ORDINAL_POSITION
    `, [process.env.DATABASE_NAME || 'hifnf_db']);

    console.log('Badge fields in subscription_plans table:');
    (updatedColumns as any[]).forEach(col => {
      console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE} (default: ${col.COLUMN_DEFAULT})`);
    });

  } catch (error) {
    console.error('Error adding badge fields to subscription_plans table:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
if (require.main === module) {
  addBadgeFieldsToSubscriptionPlans()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default addBadgeFieldsToSubscriptionPlans;
