#!/usr/bin/env tsx

/**
 * Migration script to fix users table schema
 * Adds missing columns that are defined in the schema but not in the database
 */

import mysql from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function addMissingColumns() {
  console.log('🔧 Fixing users table schema...');
  
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });

  try {
    // Check if columns exist before adding them
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
    `, [dbConfig.database]);

    const existingColumns = (columns as any[]).map(col => col.COLUMN_NAME);
    console.log('📋 Existing columns:', existingColumns);

    const columnsToAdd = [
      {
        name: 'is_verified',
        sql: 'ADD COLUMN is_verified BOOLEAN DEFAULT FALSE'
      }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding column: ${column.name}`);
        await connection.execute(`ALTER TABLE users ${column.sql}`);
        console.log(`✅ Added column: ${column.name}`);
      } else {
        console.log(`⏭️  Column ${column.name} already exists, skipping`);
      }
    }

    console.log('🎉 Schema migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function verifySchema() {
  console.log('\n🔍 Verifying schema after migration...');
  
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });

  try {
    // Test a simple query
    const [result] = await connection.execute(`
      SELECT id, name, email, is_admin, is_verified 
      FROM users 
      LIMIT 1
    `);
    
    console.log('✅ Schema verification successful!');
    console.log(`📊 Query returned ${(result as any[]).length} row(s)`);
    
  } catch (error) {
    console.error('❌ Schema verification failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function main() {
  console.log('🚀 Starting users table schema migration...\n');
  
  try {
    await addMissingColumns();
    await verifySchema();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('💡 You can now restart your application and the API endpoints should work.');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
main().catch((error) => {
  console.error('💥 Migration script failed:', error);
  process.exit(1);
});
