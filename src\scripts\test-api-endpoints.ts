#!/usr/bin/env tsx

/**
 * Test script to diagnose API endpoint issues
 * This script tests database connectivity and API endpoints
 */

import { db } from '../lib/db';
import { users, subscriptionPlans } from '../lib/db/schema';
import { eq } from 'drizzle-orm';

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test basic database connectivity
    const result = await db.select().from(users).limit(1);
    console.log('✅ Database connection successful');
    console.log(`📊 Found ${result.length} user(s) in database`);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

async function testUsersQuery() {
  console.log('\n🔍 Testing users query...');
  
  try {
    // Test the same query used in the API route
    const usersResult = await db.select({
      id: users.id,
      name: users.name,
      email: users.email,
      isAdmin: users.isAdmin,
    }).from(users).limit(5);
    
    console.log('✅ Users query successful');
    console.log(`📊 Found ${usersResult.length} user(s)`);
    console.log('👤 Sample users:', usersResult.map(u => ({ name: u.name, email: u.email, isAdmin: u.isAdmin })));
    return true;
  } catch (error) {
    console.error('❌ Users query failed:', error);
    return false;
  }
}

async function testSubscriptionPlansQuery() {
  console.log('\n🔍 Testing subscription plans query...');
  
  try {
    // Test the same query used in the API route
    const plansResult = await db.select().from(subscriptionPlans).limit(5);
    
    console.log('✅ Subscription plans query successful');
    console.log(`📊 Found ${plansResult.length} plan(s)`);
    console.log('💳 Sample plans:', plansResult.map(p => ({ name: p.name, price: p.price })));
    return true;
  } catch (error) {
    console.error('❌ Subscription plans query failed:', error);
    return false;
  }
}

async function testAdminUser() {
  console.log('\n🔍 Testing for admin users...');
  
  try {
    const adminUsers = await db.select({
      id: users.id,
      name: users.name,
      email: users.email,
      isAdmin: users.isAdmin,
    }).from(users).where(eq(users.isAdmin, true)).limit(5);
    
    console.log('✅ Admin users query successful');
    console.log(`👑 Found ${adminUsers.length} admin user(s)`);
    
    if (adminUsers.length === 0) {
      console.log('⚠️  No admin users found. This might be why API calls are failing with 401 Unauthorized.');
      console.log('💡 Consider creating an admin user or checking the authentication setup.');
    } else {
      console.log('👑 Admin users:', adminUsers.map(u => ({ name: u.name, email: u.email })));
    }
    
    return adminUsers.length > 0;
  } catch (error) {
    console.error('❌ Admin users query failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting API endpoints diagnostic test...\n');
  
  const tests = [
    { name: 'Database Connection', test: testDatabaseConnection },
    { name: 'Users Query', test: testUsersQuery },
    { name: 'Subscription Plans Query', test: testSubscriptionPlansQuery },
    { name: 'Admin Users Check', test: testAdminUser },
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    try {
      const result = await test();
      results.push({ name, success: result });
    } catch (error) {
      console.error(`❌ Test "${name}" threw an error:`, error);
      results.push({ name, success: false });
    }
  }
  
  console.log('\n📋 Test Results Summary:');
  console.log('========================');
  
  results.forEach(({ name, success }) => {
    console.log(`${success ? '✅' : '❌'} ${name}: ${success ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = results.every(r => r.success);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! The database and queries are working correctly.');
    console.log('🔍 If you\'re still seeing API errors, check:');
    console.log('   - Authentication/session issues');
    console.log('   - Network connectivity');
    console.log('   - Server startup errors');
  } else {
    console.log('\n⚠️  Some tests failed. Please address the issues above.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the diagnostic
main().catch((error) => {
  console.error('💥 Diagnostic script failed:', error);
  process.exit(1);
});
