import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import { sql } from 'drizzle-orm';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log("Starting store reviews table migration...");
  
  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
    });

    const db = drizzle(connection);
    
    console.log("Connected to database successfully!");
    
    // Check if store_reviews table exists
    const [tables] = await connection.execute(`
      SHOW TABLES LIKE 'store_reviews'
    `);
    
    if ((tables as any[]).length === 0) {
      console.log("Store reviews table does not exist. Creating it...");
      
      await connection.execute(`
        CREATE TABLE store_reviews (
          id VARCHAR(255) PRIMARY KEY,
          userId VARCHAR(255) NOT NULL,
          storeId VARCHAR(255) NOT NULL,
          rating INT NOT NULL,
          comment TEXT,
          isApproved BOOLEAN DEFAULT TRUE,
          isReported BOOLEAN DEFAULT FALSE,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
        )
      `);
      
      console.log("Store reviews table created successfully!");
    } else {
      console.log("Store reviews table exists. Checking for missing columns...");
      
      // Check for isApproved column
      const [isApprovedColumn] = await connection.execute(`
        SHOW COLUMNS FROM store_reviews LIKE 'isApproved'
      `);
      
      if ((isApprovedColumn as any[]).length === 0) {
        console.log("Adding isApproved column...");
        await connection.execute(`
          ALTER TABLE store_reviews
          ADD COLUMN isApproved BOOLEAN DEFAULT TRUE
        `);
        console.log("isApproved column added successfully!");
      } else {
        console.log("isApproved column already exists.");
      }
      
      // Check for isReported column
      const [isReportedColumn] = await connection.execute(`
        SHOW COLUMNS FROM store_reviews LIKE 'isReported'
      `);
      
      if ((isReportedColumn as any[]).length === 0) {
        console.log("Adding isReported column...");
        await connection.execute(`
          ALTER TABLE store_reviews
          ADD COLUMN isReported BOOLEAN DEFAULT FALSE
        `);
        console.log("isReported column added successfully!");
      } else {
        console.log("isReported column already exists.");
      }
    }
    
    await connection.end();
    console.log("Migration completed successfully!");
    
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

main();
