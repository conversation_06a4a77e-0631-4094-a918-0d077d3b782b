"use client";

import { useState, useEffect, useCallback, memo } from "react";
import Image from "next/image";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import {
  EllipsisHorizontalIcon,
  XMarkIcon,
  ShareIcon,
  HeartIcon,
  BookmarkIcon,
  FlagIcon,
  ClockIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleOvalLeftIcon,
  FireIcon,
  EyeIcon
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartSolidIcon,
  BookmarkIcon as BookmarkSolidIcon
} from "@heroicons/react/24/solid";
import { Badge } from "@/components/ui/Badge";
import { formatTimeAgo } from "@/lib/utils";
import { parseTextContent, renderParsedContent } from "@/lib/utils/link-utils";
import { CommentSection } from "./CommentSection";
import { FanPageCommentSection } from "../fan-pages/FanPageCommentSection";
import { ShareModal } from "./ShareModal";
import { Button } from "@/components/ui/Button";
import { PostTime } from "@/components/ui/TimeDisplay";
import eventBus from "@/lib/eventBus";
import { Post } from "@/types/post";
import { cn } from "@/lib/utils";
import { usePostCard } from "@/hooks/usePostCard";
import { useSwipeGesture } from "@/hooks/useSwipeGesture";
import { haptic } from "@/utils/haptics";
import { PostMediaGallery } from "./PostMediaGallery";
import { ClockIcon, CalendarIcon } from "@heroicons/react/24/outline";
import { PostActionButtons } from "./PostActionButtons";
import { ReactionModal } from "./ReactionModal";
import { ReactionsList } from "./ReactionsList";
import { UserWithBadge } from "@/components/ui/UserWithBadge";


// Remove the local Post interface since we're using the shared one
/*interface Post {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  createdAt: string;
  type: 'user_post' | 'fan_page_post' | 'group_post';
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  } | null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  } | null;
  group: {
    id: string;
    name: string;
    slug: string;
    profileImage: string | null;
    isPrivate: boolean;
  } | null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
  sharedPost?: {
    id: string;
    content: string;
    images: string[] | null;
    videos: string[] | null;
    backgroundColor: string | null;
    feeling: string | null;
    activity: string | null;
    location: string | null;
    formattedContent: boolean | null;
    createdAt: string;
    user: {
      id: string;
      name: string;
      username?: string | null;
      image: string | null;
    };
  } | null;
}*/

interface PostCardProps {
  post: Post & { isBookmarked?: boolean };
  onLike: () => void;
  onDislike: () => void;
  className?: string;
  variant?: "default" | "compact" | "detailed" | "saved";
  showActions?: boolean;
  showComments?: boolean;
  autoPlayVideo?: boolean;
  onUnsave?: () => void;
}

// Enhanced PostCard component with improved design and functionality
export const PostCard = memo(function PostCard({
  post,
  onLike,
  onDislike,
  className,
  variant = "default",
  showActions = true,
  showComments: showCommentsDefault = true,
  autoPlayVideo = false,
  onUnsave
}: PostCardProps) {
  // Swipe gesture for mobile interactions
  const { swipeRef } = useSwipeGesture({
    onSwipeLeft: () => {
      // Quick like on swipe left
      if (!post.liked) {
        haptic.light();
        handleLike();
      }
    },
    onSwipeRight: () => {
      // Quick share on swipe right
      haptic.medium();
      setIsShareModalOpen(true);
    },
    threshold: 100,
    enabled: true
  });

  // Use the custom hook for all PostCard logic
  const {
    showComments,
    setShowComments,
    commentCount,
    isEditModalOpen,
    setIsEditModalOpen,
    isShareModalOpen,
    setIsShareModalOpen,
    editContent,
    setEditContent,
    isSubmitting,
    error,
    isExpanded,
    setIsExpanded,
    showSeeMore,
    isBookmarked,
    isLikeAnimating,
    isDislikeAnimating,
    imageLoadError,
    isHovered,
    setIsHovered,
    isReactionModalOpen,
    setIsReactionModalOpen,
    isCurrentUserPost,
    isFanPagePost,
    isGroupPost,
    displayInfo,
    handleLike,
    handleDislike,
    handleBookmark,
    handleImageError,
    handleEditPost,
    handleSaveEdit,
    handleDeletePost,
    editImages,
    setEditImages,
    handleImageUpload,
    handleRemoveImage,
  } = usePostCard({ post, onLike, onDislike });





  return (
    <>
      {/* Edit Post Modal */}
      <Dialog open={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <DialogPanel className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <DialogTitle className="text-lg font-medium">Edit Post</DialogTitle>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="rounded-full p-1 hover:bg-gray-200"
              >
                <XMarkIcon className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            {error && (
              <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-600">
                {error}
              </div>
            )}

            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full rounded-md border border-gray-300 p-3 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              rows={5}
              placeholder="What's on your mind?"
            />

            {/* Image Upload Section */}
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">Images</label>
                <label className="cursor-pointer inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Images
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        handleImageUpload(e.target.files);
                      }
                    }}
                  />
                </label>
              </div>

              {/* Image Preview Grid */}
              {editImages.length > 0 && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {editImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={imageUrl}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-20 object-cover rounded-md border border-gray-200"
                      />
                      <button
                        onClick={() => handleRemoveImage(index)}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={isSubmitting || !editContent || !editContent.trim()}
                isLoading={isSubmitting}
              >
                Save
              </Button>
            </div>
          </DialogPanel>
        </div>
      </Dialog>

      {/* Share Modal */}
      <ShareModal
        post={post}
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
      />



      <article
        ref={swipeRef}
        className={cn(
          "relative overflow-hidden bg-white",
          "border border-gray-100",
          "shadow-sm",
          "h-full flex flex-col",
          {
            "rounded-xl": variant === "default",
            "rounded-lg": variant === "compact",
            "rounded-2xl": variant === "detailed",
          },
          className
        )}
        role="article"
        aria-label={`Post by ${displayInfo.name}`}
      >

        {/* Post header section with padding */}
        <div className="relative p-3 sm:p-4 lg:p-6 z-10">
          {/* Enhanced Post header */}
          <header className="flex items-center justify-between mb-3 sm:mb-4">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              {/* Enhanced profile image with status indicator */}
              <div className="relative flex-shrink-0">
                <div className="relative h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100 overflow-hidden shadow-md border-2 border-white group-hover:shadow-lg transition-all duration-300">
                  {displayInfo.image ? (
                    <OptimizedImage
                      src={displayInfo.image}
                      alt={`${displayInfo.name}'s profile picture`}
                      width={48}
                      height={48}
                      className="rounded-full object-cover transition-transform duration-300 group-hover:scale-110 w-10 h-10 sm:w-12 sm:h-12"
                      loadingClassName="bg-gray-200 animate-pulse"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 text-white text-lg font-bold">
                      {displayInfo.name?.charAt(0).toUpperCase() || "U"}
                    </div>
                  )}
                </div>

                {/* Online status indicator (if available) */}
                <div className="absolute -bottom-0.5 -right-0.5 h-3.5 w-3.5 bg-green-400 border-2 border-white rounded-full shadow-sm" />
              </div>
              {/* Enhanced user info section */}
              <div className="min-w-0 flex-1">
                {/* Group post special layout */}
                {isGroupPost ? (
                  <div className="space-y-1">
                    {/* Group name first */}
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <Link
                        href={displayInfo.groupUrl || '#'}
                        className="text-sm sm:text-base font-semibold text-black hover:text-blue-600 transition-colors duration-200 truncate"
                      >
                        {displayInfo.groupName}
                      </Link>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-emerald-200 text-green-800 border border-green-200 shadow-sm">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Group
                      </span>
                    </div>
                    {/* Author name second */}
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <span>by</span>
                      <UserWithBadge
                        user={{
                          id: post.user?.id || '',
                          name: displayInfo.authorName || '',
                          username: post.user?.username
                        }}
                        linkToProfile={true}
                        badgeSize="sm"
                        nameClassName="font-medium hover:text-blue-600 transition-colors duration-200"
                      />
                    </div>
                  </div>
                ) : (
                  /* Regular layout for user and fan page posts */
                  <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                    <UserWithBadge
                      user={{
                        id: post.user?.id || post.fanPage?.id || '',
                        name: displayInfo.name,
                        username: post.user?.username || post.fanPage?.username
                      }}
                      linkToProfile={true}
                      badgeSize="sm"
                      nameClassName="text-sm sm:text-base font-semibold text-black hover:text-blue-600 transition-colors duration-200 truncate"
                    />

                    {/* Enhanced verification badge */}
                    {displayInfo.isVerified && (
                      <div className="flex-shrink-0">
                        <svg className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20" aria-label="Verified account">
                          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}

                    {/* Enhanced post type badges */}
                    {isFanPagePost && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-200 shadow-sm">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                        </svg>
                        Page
                      </span>
                    )}
                    {post.type === 'blog_post' && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-orange-100 to-red-200 text-orange-800 border border-orange-200 shadow-sm">
                        <FireIcon className="w-3 h-3 mr-1" />
                        Blog Post
                      </span>
                    )}
                  </div>
                )}

                {/* Enhanced timestamp and metadata */}
                <div className="flex items-center space-x-2 text-xs text-black">
                  <PostTime
                    date={post.createdAt}
                    className="hover:text-gray-700 transition-colors duration-200"
                    autoUpdate={true}
                  />

                  {/* Privacy indicator */}
                  <span className="text-gray-400">•</span>
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clipRule="evenodd" />
                    </svg>
                    <span className="capitalize">{post.privacy}</span>
                  </div>

                  {/* Show subtitle only for non-group posts */}
                  {displayInfo.subtitle && !isGroupPost && (
                    <>
                      <span className="text-gray-400">•</span>
                      <Link
                        href={`/groups/${post.group?.slug || post.group?.id}`}
                        className="text-blue-600 hover:text-blue-800 transition-colors duration-200 font-medium"
                      >
                        {displayInfo.subtitle}
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced action menu */}
            <div className="flex items-center space-x-2">
              {/* Bookmark button for all users */}
              <button
                onClick={variant === "saved" && onUnsave ? onUnsave : handleBookmark}
                className="flex items-center justify-center h-9 w-9 rounded-full hover:bg-gray-100 transition-all duration-200 group/bookmark"
                aria-label={
                  variant === "saved"
                    ? "Remove from saved"
                    : (post.isBookmarked || isBookmarked)
                      ? "Remove bookmark"
                      : "Bookmark post"
                }
              >
                {(post.isBookmarked || isBookmarked) ? (
                  <BookmarkSolidIcon className="h-5 w-5 text-blue-600 group-hover/bookmark:scale-110 transition-transform duration-200" />
                ) : (
                  <BookmarkIcon className="h-5 w-5 text-gray-500 group-hover/bookmark:text-blue-600 group-hover/bookmark:scale-110 transition-all duration-200" />
                )}
              </button>

              {/* Three-dot menu for post owner */}
              {isCurrentUserPost && (
                <Menu as="div" className="relative">
                  <MenuButton className="flex items-center justify-center h-9 w-9 rounded-full hover:bg-gray-100 transition-colors duration-200 group/menu">
                    <EllipsisHorizontalIcon className="h-5 w-5 text-gray-500 group-hover/menu:text-gray-700" />
                  </MenuButton>
                  <MenuItems anchor="bottom end" className="absolute z-20 mt-2 w-48 origin-top-right rounded-xl bg-white py-2 shadow-xl ring-1 ring-black/5 focus:outline-none border border-gray-100 backdrop-blur-sm">
                    <MenuItem>
                      <button
                        onClick={handleEditPost}
                        className="flex items-center w-full text-left px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200 group/edit"
                        disabled={isSubmitting}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-gray-500 group-hover/edit:text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit Post
                      </button>
                    </MenuItem>
                    <MenuItem>
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
                            handleDeletePost();
                          }
                        }}
                        className="flex items-center w-full text-left px-4 py-2.5 text-sm text-red-700 hover:bg-red-50 transition-colors duration-200 group/delete"
                        disabled={isSubmitting}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-red-500 group-hover/delete:text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete Post
                      </button>
                    </MenuItem>

                  </MenuItems>
                </Menu>
              )}
            </div>
          </header>

          {/* Scheduled Post Indicator */}
          {post.scheduledAt && !post.isPublished && (
            <div className="mb-4 p-3 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <ClockIcon className="h-5 w-5 text-amber-600 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200">
                      <CalendarIcon className="w-3 h-3 mr-1" />
                      Scheduled
                    </span>
                    <span className="text-sm text-amber-700 font-medium">
                      Will be published on {new Date(post.scheduledAt).toLocaleString()}
                    </span>
                  </div>
                  <p className="text-xs text-amber-600 mt-1">
                    This post is scheduled and will be automatically published at the specified time.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Blog-specific metadata */}
          {post.type === 'blog_post' && post.blog && (
            <div className="mb-4 space-y-3">
              {/* Blog title and featured badge */}
              <div className="flex items-start justify-between">
                <Link href={`/blogs/${post.blog.slug}`} className="flex-1">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                    {post.blog.title}
                  </h2>
                </Link>
                {post.blog.featured && (
                  <Badge variant="secondary" className="ml-3 bg-yellow-100 text-yellow-800 border-yellow-200">
                    Featured
                  </Badge>
                )}
              </div>

              {/* Blog metadata */}
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                {post.blog.readTime && (
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="h-3 w-3" />
                    <span>{post.blog.readTime} min read</span>
                  </div>
                )}
                {post.blog.viewCount !== undefined && (
                  <div className="flex items-center space-x-1">
                    <EyeIcon className="h-3 w-3" />
                    <span>{post.blog.viewCount} views</span>
                  </div>
                )}
                {post.blog.category && (
                  <Badge
                    variant="outline"
                    className="text-xs"
                    style={{
                      borderColor: post.blog.category.color,
                      color: post.blog.category.color
                    }}
                  >
                    {post.blog.category.name}
                  </Badge>
                )}
              </div>

              {/* Blog tags */}
              {post.blog.tags && post.blog.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {post.blog.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                    >
                      #{tag}
                    </span>
                  ))}
                  {post.blog.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-500">
                      +{post.blog.tags.length - 3} more
                    </span>
                  )}
                </div>
              )}
            </div>
          )}

        {/* Enhanced Post content */}
        {(post.content || (post.type === 'blog_post' && post.blog?.excerpt)) && (
          <div
            className={cn(
              "whitespace-pre-line leading-relaxed",
              post.backgroundColor ?
                "flex items-center justify-center text-center py-6 sm:py-8 text-base sm:text-lg font-medium text-black shadow-sm mb-2" :
                "text-sm sm:text-base text-black pt-2 pb-1 mx-3 sm:mx-4 mb-3 sm:mb-4"
            )}
            style={post.backgroundColor ?
              {
                background: post.backgroundColor.includes('gradient') ? post.backgroundColor : undefined,
                backgroundColor: !post.backgroundColor.includes('gradient') ? post.backgroundColor : undefined,
                textShadow: '0 2px 4px rgba(255,255,255,0.8)',
                minHeight: '200px'
              } : {}
            }
          >
            {/* Enhanced Feeling/Activity/Location display */}
            {(post.feeling || post.activity || post.location) && (
              <div className="mb-3 flex flex-wrap gap-2">
                {post.feeling && (
                  <span className={cn(
                    "inline-flex items-center rounded-full px-3 py-1 text-xs font-medium shadow-sm",
                    post.backgroundColor ?
                      "bg-white bg-opacity-20 text-black border border-white border-opacity-20" :
                      "bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 border border-blue-200"
                  )}>
                    <svg className="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                    Feeling {post.feeling}
                  </span>
                )}
                {post.activity && (
                  <span className={cn(
                    "inline-flex items-center rounded-full px-3 py-1 text-xs font-medium shadow-sm",
                    post.backgroundColor ?
                      "bg-white bg-opacity-20 text-black border border-white border-opacity-20" :
                      "bg-gradient-to-r from-green-50 to-emerald-100 text-green-700 border border-green-200"
                  )}>
                    <svg className="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {post.activity}
                  </span>
                )}
                {post.location && (
                  <span className={cn(
                    "inline-flex items-center rounded-full px-3 py-1 text-xs font-medium shadow-sm",
                    post.backgroundColor ?
                      "bg-white bg-opacity-20 text-black border border-white border-opacity-20" :
                      "bg-gradient-to-r from-amber-50 to-orange-100 text-amber-700 border border-amber-200"
                  )}>
                    <svg className="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    {post.location}
                  </span>
                )}
              </div>
            )}

            {/* Enhanced content with better typography and link detection */}
            {post.content && (
              <div className={cn(
                "prose prose-sm max-w-none",
                post.backgroundColor && "prose-invert"
              )}>
                {showSeeMore && !isExpanded ? (
                <div className="space-y-2">
                  <div className={cn(
                    "leading-relaxed",
                    post.backgroundColor ? "text-black" : "text-black"
                  )}>
                    {renderParsedContent(
                      parseTextContent(post.content?.substring(0, 300) + "..."),
                      {
                        showLinkPreviews: false,
                        linkClassName: "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
                        mentionClassName: "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
                      }
                    )}
                  </div>
                  <button
                    onClick={() => setIsExpanded(true)}
                    className={cn(
                      "inline-flex items-center font-medium transition-all duration-200 hover:underline focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-sm",
                      post.backgroundColor ?
                        "text-black hover:text-gray-700 focus:ring-black" :
                        "text-blue-600 hover:text-blue-800 focus:ring-blue-500"
                    )}
                  >
                    See More
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className={cn(
                    "leading-relaxed",
                    post.backgroundColor ? "text-black" : "text-black"
                  )}>
                    {renderParsedContent(
                      parseTextContent(post.content),
                      {
                        showLinkPreviews: true,
                        linkClassName: "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
                        mentionClassName: "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
                      }
                    )}
                  </div>
                  {showSeeMore && isExpanded && (
                    <button
                      onClick={() => setIsExpanded(false)}
                      className={cn(
                        "inline-flex items-center font-medium transition-all duration-200 hover:underline focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-sm",
                        post.backgroundColor ?
                          "text-black hover:text-gray-700 focus:ring-black" :
                          "text-blue-600 hover:text-blue-800 focus:ring-blue-500"
                      )}
                    >
                      See Less
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                      </svg>
                    </button>
                  )}
                </div>
              )}
              </div>
            )}

            {/* Blog-specific content rendering */}
            {post.type === 'blog_post' && post.blog?.excerpt && !post.content && (
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed mb-4">
                  {post.blog.excerpt}
                </p>
                <Link
                  href={`/blogs/${post.blog.slug}`}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Read Full Article
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            )}
          </div>
        )}

        {/* Shared post content */}
        {post.sharedPost && (
          <Link
            href={`/posts/${post.sharedPost.id}`}
            className="block mt-4 rounded-xl border border-gray-200 p-4 shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-200 cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              <div className="h-9 w-9 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 overflow-hidden shadow-sm border border-gray-100">
                {post.sharedPost.user.image ? (
                  <OptimizedImage
                    src={post.sharedPost.user.image}
                    alt={post.sharedPost.user.name}
                    width={36}
                    height={36}
                    className="rounded-full object-cover"
                    loadingClassName="bg-gray-200 animate-pulse"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-bold">
                    {post.sharedPost.user.name?.charAt(0).toUpperCase() || "U"}
                  </div>
                )}
              </div>
              <div>
                <span className="text-xs font-semibold text-black hover:text-blue-600 transition-colors duration-200">
                  {post.sharedPost.user.name}
                </span>
                <p className="text-xs text-black mt-0.5">
                  {formatTimeAgo(new Date(post.sharedPost.createdAt))}
                </p>
              </div>
            </div>

            {/* Shared post content */}
            <div
              className={`mt-3 text-sm text-black whitespace-pre-line rounded-xl p-3 ${post.sharedPost.backgroundColor ? 'shadow-sm' : ''}`}
              style={post.sharedPost.backgroundColor ? { backgroundColor: post.sharedPost.backgroundColor } : {}}
            >
              {/* Feeling/Activity display */}
              {(post.sharedPost.feeling || post.sharedPost.activity || post.sharedPost.location) && (
                <div className="mb-2 text-xs text-black flex flex-wrap gap-2">
                  {post.sharedPost.feeling && (
                    <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-600">
                      Feeling {post.sharedPost.feeling}
                    </span>
                  )}
                  {post.sharedPost.activity && (
                    <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-0.5 text-xs font-medium text-green-600">
                      {post.sharedPost.activity}
                    </span>
                  )}
                  {post.sharedPost.location && (
                    <span className="inline-flex items-center rounded-full bg-amber-50 px-2 py-0.5 text-xs font-medium text-amber-600">
                      📍 {post.sharedPost.location}
                    </span>
                  )}
                </div>
              )}

              {renderParsedContent(
                parseTextContent(post.sharedPost.content),
                {
                  showLinkPreviews: false,
                  linkClassName: "text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",
                  mentionClassName: "text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
                }
              )}
            </div>

            {/* Enhanced Shared post media with gallery */}
            <PostMediaGallery
              images={post.sharedPost.images}
              videos={post.sharedPost.videos}
              autoPlayVideo={false}
              className="mt-2"
            />
          </Link>
        )}
        </div>

        {/* Enhanced Post media with gallery - borderless, full width with reduced spacing */}
        {/* Only show main post media if it's not a shared post */}
        {!post.sharedPost && (
          <div className="-mt-2 -mb-2">
            <PostMediaGallery
              images={post.images}
              videos={post.videos}
              autoPlayVideo={autoPlayVideo}
              onImageError={handleImageError}
              borderless={true}
            />
          </div>
        )}



        {/* Post stats and actions section with padding */}
        <div className="relative p-3 sm:p-4 lg:p-6 z-10">
        {/* Enhanced Post stats */}
        <div className="mt-3 sm:mt-4 flex items-center justify-between px-1 text-xs">
          {/* Enhanced reaction indicators */}
          <div className="flex items-center space-x-3">
            {(post._count.likes > 0 || post._count.dislikes > 0) && (
              <button
                onClick={() => setIsReactionModalOpen(true)}
                className="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-1 transition-colors duration-200"
              >
                <div className="flex items-center space-x-2">
                  <div className="flex -space-x-1">
                    {post._count.likes > 0 && (
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md border-2 border-white z-10">
                        <HandThumbUpIcon className="h-3 w-3" />
                      </div>
                    )}
                    {post._count.dislikes > 0 && (
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md border-2 border-white">
                        <HandThumbDownIcon className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  <ReactionsList postId={post.id} maxDisplay={3} />
                </div>
                <span className="font-medium text-black hover:text-gray-900 transition-colors duration-200">
                  {post._count.likes + post._count.dislikes}
                  <span className="text-black ml-1">
                    {post._count.likes + post._count.dislikes === 1 ? "reaction" : "reactions"}
                  </span>
                </span>
              </button>
            )}
          </div>

          {/* Enhanced interaction stats */}
          <div className="flex items-center space-x-4 text-black">
            {commentCount > 0 && showCommentsDefault && (
              <button
                onClick={() => setShowComments(!showComments)}
                className="hover:text-blue-600 transition-colors duration-200 font-medium flex items-center space-x-1"
                aria-label={`${commentCount} comments`}
              >
                <ChatBubbleOvalLeftIcon className="h-3.5 w-3.5" />
                <span>
                  {commentCount} {commentCount === 1 ? "comment" : "comments"}
                </span>
              </button>
            )}

            {post._count.shares > 0 && (
              <div className="flex items-center space-x-1 text-black">
                <ShareIcon className="h-3.5 w-3.5" />
                <span className="font-medium">
                  {post._count.shares} {post._count.shares === 1 ? "share" : "shares"}
                </span>
              </div>
            )}

            {/* View count (if available) */}
            {post._count && 'views' in post._count && (post._count as any).views > 0 && (
              <div className="flex items-center space-x-1 text-black">
                <EyeIcon className="h-3.5 w-3.5" />
                <span className="font-medium">
                  {(post._count as any).views} {(post._count as any).views === 1 ? "view" : "views"}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Spacer to push actions to bottom */}
        <div className="flex-1"></div>

        {/* Spacer to push actions to bottom */}
        <div className="flex-1"></div>

        {/* Enhanced Post actions */}
        {showActions && (
          <PostActionButtons
            liked={post.liked}
            onLike={handleLike}
            isLikeAnimating={isLikeAnimating}
            disliked={post.disliked}
            onDislike={handleDislike}
            isDislikeAnimating={isDislikeAnimating}
            showComments={showComments}
            onToggleComments={() => setShowComments(!showComments)}
            showCommentsButton={showCommentsDefault}
            onShare={() => setIsShareModalOpen(true)}
            postType={post.type}
          />
        )}

        {/* Enhanced Comments section */}
        {showComments && showCommentsDefault && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            {post.type === 'fan_page_post' ? (
              <FanPageCommentSection
                postId={post.id}
                showComments={true}
                initialCommentCount={post._count.comments}
              />
            ) : (
              <CommentSection
                postId={post.id}
                postType={post.type}
                blogSlug={post.type === 'blog_post' ? post.blog?.slug : undefined}
              />
            )}
          </div>
        )}
        </div>
      </article>

      {/* Reaction Modal */}
      <ReactionModal
        isOpen={isReactionModalOpen}
        onClose={() => setIsReactionModalOpen(false)}
        postId={post.id}
      />
    </>
  );
});
