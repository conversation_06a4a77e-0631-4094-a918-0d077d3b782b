import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { posts, fanPagePosts } from "@/lib/db/schema";
import { and, eq, lte } from "drizzle-orm";

// This endpoint should be called by a cron job to publish scheduled posts
export async function POST(request: Request) {
  try {
    // Verify this is a legitimate cron request (you might want to add authentication)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const now = new Date();

    // Find and publish scheduled regular posts that are due
    const scheduledPosts = await db
      .select()
      .from(posts)
      .where(
        and(
          eq(posts.isPublished, false),
          lte(posts.scheduledAt, now)
        )
      );

    // Update regular posts to published
    if (scheduledPosts.length > 0) {
      const postIds = scheduledPosts.map(post => post.id);
      
      await db
        .update(posts)
        .set({ 
          isPublished: true,
          updatedAt: now
        })
        .where(
          and(
            eq(posts.isPublished, false),
            lte(posts.scheduledAt, now)
          )
        );
    }

    // Find and publish scheduled fan page posts that are due
    const scheduledFanPagePosts = await db
      .select()
      .from(fanPagePosts)
      .where(
        and(
          eq(fanPagePosts.isPublished, false),
          lte(fanPagePosts.scheduledAt, now)
        )
      );

    // Update fan page posts to published
    if (scheduledFanPagePosts.length > 0) {
      await db
        .update(fanPagePosts)
        .set({ 
          isPublished: true,
          updatedAt: now
        })
        .where(
          and(
            eq(fanPagePosts.isPublished, false),
            lte(fanPagePosts.scheduledAt, now)
          )
        );
    }

    const totalPublished = scheduledPosts.length + scheduledFanPagePosts.length;

    return NextResponse.json({
      message: `Successfully published ${totalPublished} scheduled posts`,
      publishedPosts: scheduledPosts.length,
      publishedFanPagePosts: scheduledFanPagePosts.length,
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error("Error publishing scheduled posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Allow GET requests for testing
export async function GET() {
  return NextResponse.json({
    message: "Scheduled posts publisher endpoint",
    usage: "POST to this endpoint to publish due scheduled posts"
  });
}
