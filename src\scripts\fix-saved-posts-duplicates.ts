import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function fixSavedPostsDuplicates() {
  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    console.log("Starting to fix saved posts duplicates...");

    // First, remove duplicate entries keeping only the earliest one
    await connection.query(`
      DELETE sp1 FROM savedPosts sp1
      INNER JOIN savedPosts sp2 
      WHERE sp1.userId = sp2.userId 
        AND sp1.postId = sp2.postId 
        AND sp1.createdAt > sp2.createdAt
    `);

    console.log("Removed duplicate saved posts entries.");

    // Check if unique constraint already exists
    const [constraints] = await connection.query(`
      SELECT CONSTRAINT_NAME 
      FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
      WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME = 'savedPosts' 
        AND CONSTRAINT_TYPE = 'UNIQUE'
        AND CONSTRAINT_NAME = 'unique_user_post'
    `, [dbName]);

    // @ts-ignore
    if (constraints.length === 0) {
      // Add unique constraint to prevent future duplicates
      await connection.query(`
        ALTER TABLE savedPosts 
        ADD CONSTRAINT unique_user_post 
        UNIQUE (userId, postId)
      `);
      console.log("Added unique constraint to prevent duplicate saved posts.");
    } else {
      console.log("Unique constraint already exists.");
    }

    console.log("Successfully fixed saved posts duplicates!");
  } catch (error) {
    console.error("Error fixing saved posts duplicates:", error);
  } finally {
    await connection.end();
  }
}

fixSavedPostsDuplicates().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
