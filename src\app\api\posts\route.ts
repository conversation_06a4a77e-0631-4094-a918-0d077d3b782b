import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, subscriptions, savedPosts } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { desc, eq, or, and, sql } from "drizzle-orm";

const postSchema = z.object({
  content: z.string().max(5000).optional().default(''),
  images: z.array(z.string().url()).optional(),
  videos: z.array(z.string().url()).optional(),
  privacy: z.enum(["public", "subscribers", "private"]).default("public"),
  sharedPostId: z.string().optional(),
  backgroundColor: z.string().optional(),
  feeling: z.string().optional(),
  activity: z.string().optional(),
  location: z.string().optional(),
  formattedContent: z.boolean().optional(),
  isScheduled: z.boolean().optional().default(false),
  scheduledDate: z.string().optional(),
  scheduledTime: z.string().optional(),
  scheduledDateTime: z.string().optional(),

}).refine(data => {
  // Either content, images, or videos must be provided
  return !!data.content ||
         (data.images && data.images.length > 0) ||
         (data.videos && data.videos.length > 0);
}, {
  message: "Post must contain either text content, images, or videos",
  path: ["content"],
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get pagination parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Max 20 posts
    const offset = (page - 1) * limit;

    // Get user's subscriptions to filter content
    const userSubscriptions = await db.query.subscriptions.findMany({
      where: eq(subscriptions.subscriberId, session.user.id),
      columns: {
        targetUserId: true,
      },
    });

    const subscribedUserIds = userSubscriptions.map(sub => sub.targetUserId);

    // Fetch posts from the database with subscription-based filtering
    const allPosts = await db.query.posts.findMany({
      where: and(
        // Only show published posts
        eq(posts.isPublished, true),
        or(
          // Public posts from anyone
          eq(posts.privacy, 'public'),
          // User's own posts (all privacy levels)
          eq(posts.userId, session.user.id),
          // Subscribers-only posts from users the current user is subscribed to
          ...(subscribedUserIds.length > 0 ? [
            and(
              eq(posts.privacy, 'subscribers'),
              sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
            )
          ] : [])
        )
      ),
      orderBy: [desc(posts.createdAt)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    // Format posts for the frontend
    const formattedPosts = await Promise.all(
      allPosts.map(async (post) => {
        // Check if the current user has liked or disliked this post
        const userLike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'like'
        );

        const userDislike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'dislike'
        );

        // Check if the current user has bookmarked this post
        const isBookmarked = await db.query.savedPosts.findFirst({
          where: and(
            eq(savedPosts.postId, post.id),
            eq(savedPosts.userId, session.user.id)
          ),
        });

        // Count likes and dislikes
        const likesCount = post.likes.filter(like => like.type === 'like').length;
        const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

        return {
          id: post.id,
          content: post.content,
          images: post.images,
          videos: post.videos,
          privacy: post.privacy,
          backgroundColor: post.backgroundColor,
          feeling: post.feeling,
          activity: post.activity,
          location: post.location,
          formattedContent: post.formattedContent,
          createdAt: post.createdAt.toISOString(),
          type: 'user_post' as const,
          user: post.user,
          fanPage: null,
          group: null,
          _count: {
            likes: likesCount,
            dislikes: dislikesCount,
            comments: post.comments.length,
            shares: post.shares.length,
          },
          liked: !!userLike,
          disliked: !!userDislike,
          isBookmarked: !!isBookmarked,
          sharedPost: post.sharedPost ? {
            id: post.sharedPost.id,
            content: post.sharedPost.content,
            images: post.sharedPost.images,
            videos: post.sharedPost.videos,
            backgroundColor: post.sharedPost.backgroundColor,
            feeling: post.sharedPost.feeling,
            activity: post.sharedPost.activity,
            location: post.sharedPost.location,
            formattedContent: post.sharedPost.formattedContent,
            createdAt: post.sharedPost.createdAt.toISOString(),
            user: post.sharedPost.user,
          } : null,
        };
      })
    );

    // Calculate pagination metadata
    const hasMore = formattedPosts.length === limit;

    return NextResponse.json({
      data: formattedPosts,
      pagination: {
        page,
        limit,
        total: formattedPosts.length,
        hasMore,
      },
    });
  } catch (error) {
    console.error("Error fetching posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();

    // Validate the data
    const validatedData = postSchema.parse(body);

    const postId = uuidv4();

    // Handle scheduled posts
    if (validatedData.isScheduled && validatedData.scheduledDateTime) {
      const scheduledDate = new Date(validatedData.scheduledDateTime);
      const now = new Date();

      // Validate that scheduled time is in the future
      if (scheduledDate <= now) {
        return NextResponse.json(
          { message: "Scheduled time must be in the future" },
          { status: 400 }
        );
      }

      // Store as scheduled post (not published until scheduled time)
      await db.insert(posts).values({
        id: postId,
        userId: session.user.id,
        content: validatedData.content,
        images: validatedData.images || null,
        videos: validatedData.videos || null,
        privacy: validatedData.privacy,
        sharedPostId: validatedData.sharedPostId || null,
        backgroundColor: validatedData.backgroundColor || null,
        feeling: validatedData.feeling || null,
        activity: validatedData.activity || null,
        location: validatedData.location || null,
        formattedContent: validatedData.formattedContent || false,
        scheduledAt: scheduledDate,
        isPublished: false, // Not published until scheduled time
      });

      return NextResponse.json(
        {
          message: `Post scheduled successfully for ${scheduledDate.toLocaleString()}`,
          id: postId,
          scheduledFor: scheduledDate.toISOString()
        },
        { status: 201 }
      );
    }

    // Regular immediate post
    await db.insert(posts).values({
      id: postId,
      userId: session.user.id,
      content: validatedData.content,
      images: validatedData.images || null,
      videos: validatedData.videos || null,
      privacy: validatedData.privacy,
      sharedPostId: validatedData.sharedPostId || null,
      backgroundColor: validatedData.backgroundColor || null,
      feeling: validatedData.feeling || null,
      activity: validatedData.activity || null,
      location: validatedData.location || null,
      formattedContent: validatedData.formattedContent || false,
      scheduledAt: null,
      isPublished: true,
    });

    return NextResponse.json(
      { message: "Post created successfully", id: postId },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
