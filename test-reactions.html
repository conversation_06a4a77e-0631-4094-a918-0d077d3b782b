<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Reactions API</title>
</head>
<body>
    <h1>Test Reactions API</h1>
    <div>
        <label for="postId">Post ID:</label>
        <input type="text" id="postId" placeholder="Enter post ID">
        <button onclick="testReactions()">Test Reactions</button>
    </div>
    <div id="result"></div>

    <script>
        async function testReactions() {
            const postId = document.getElementById('postId').value;
            if (!postId) {
                alert('Please enter a post ID');
                return;
            }

            try {
                const response = await fetch(`http://localhost:3000/api/posts/${postId}/reactions`);
                const data = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <h3>Result:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
