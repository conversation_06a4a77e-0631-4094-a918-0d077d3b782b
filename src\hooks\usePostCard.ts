"use client";

// Post card hook - Updated to fix validation errors with null values
import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import eventBus from "@/lib/eventBus";
import { Post } from "@/types/post";
import { useToast } from "@/contexts/ToastContext";
import { useThrottle } from "@/hooks/useDebounce";

interface UsePostCardProps {
  post: Post;
  onLike: () => void;
  onDislike: () => void;
}

export function usePostCard({ post, onLike, onDislike }: UsePostCardProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const { showError } = useToast();
  
  // State management
  const [showComments, setShowComments] = useState(false);
  const [commentCount, setCommentCount] = useState(post._count.comments);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [editContent, setEditContent] = useState(post.content || '');
  const [editImages, setEditImages] = useState<string[]>(post.images || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSeeMore, setShowSeeMore] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(post.isBookmarked || false);
  const [isLikeAnimating, setIsLikeAnimating] = useState(false);
  const [isDislikeAnimating, setIsDislikeAnimating] = useState(false);
  const [imageLoadError, setImageLoadError] = useState<string[]>([]);
  const [isHovered, setIsHovered] = useState(false);
  const [isReactionModalOpen, setIsReactionModalOpen] = useState(false);

  // Computed values
  const isCurrentUserPost = useMemo(() => {
    // For user posts, check if current user is the post author
    if (post.type === 'user_post') {
      return session?.user?.id === post.user?.id;
    }

    // For group posts, check if current user is the post author
    if (post.type === 'group_post') {
      return session?.user?.id === post.user?.id;
    }

    // For fan page posts, check if current user owns the fan page
    if (post.type === 'fan_page_post') {
      return session?.user?.id === post.fanPage?.id;
    }

    return false;
  }, [post.type, post.user?.id, post.fanPage?.id, session?.user?.id]);

  const isFanPagePost = useMemo(() => post.type === 'fan_page_post', [post.type]);
  const isGroupPost = useMemo(() => post.type === 'group_post', [post.type]);

  // Get the display info (user, fan page, or group)
  const displayInfo = useMemo(() => {
    if (isFanPagePost) {
      return {
        id: post.fanPage?.id || '',
        name: post.fanPage?.name || '',
        username: post.fanPage?.username || '',
        image: post.fanPage?.profileImage || null,
        isVerified: post.fanPage?.isVerified || false,
        profileUrl: `/pages/${post.fanPage?.username || post.fanPage?.id}`,
        subtitle: null,
        groupName: null,
        groupUrl: null,
        authorName: null,
        authorUrl: null
      };
    }

    if (isGroupPost) {
      return {
        id: post.group?.id || '',
        name: post.group?.name || '',
        username: post.group?.slug || '',
        image: post.group?.profileImage || null,
        isVerified: false,
        profileUrl: `/groups/${post.group?.slug || post.group?.id}`,
        subtitle: post.user?.name || '',
        groupName: post.group?.name || '',
        groupUrl: `/groups/${post.group?.slug || post.group?.id}`,
        authorName: post.user?.name || '',
        authorUrl: `/user/${post.user?.username || post.user?.id}`
      };
    }

    return {
      id: post.user?.id || '',
      name: post.user?.name || '',
      username: post.user?.username || '',
      image: post.user?.image || null,
      isVerified: false,
      profileUrl: `/user/${post.user?.username || post.user?.id}`,
      subtitle: null,
      groupName: null,
      groupUrl: null,
      authorName: null,
      authorUrl: null
    };
  }, [isFanPagePost, isGroupPost, post.fanPage, post.user, post.group]);

  // Internal like handler
  const internalHandleLike = useCallback(async () => {
    if (isLikeAnimating || isDislikeAnimating) return;

    setIsLikeAnimating(true);
    setError(null);

    try {
      await onLike();
    } catch (error) {
      console.error('Error liking post:', error);
      const errorMessage = 'Failed to like post. Please try again.';
      setError(errorMessage);
      showError('Like Failed', errorMessage);
    } finally {
      // Use a more consistent animation duration
      setTimeout(() => setIsLikeAnimating(false), 250);
    }
  }, [onLike, isLikeAnimating, isDislikeAnimating, showError]);

  // Internal dislike handler
  const internalHandleDislike = useCallback(async () => {
    if (isDislikeAnimating || isLikeAnimating) return;

    setIsDislikeAnimating(true);
    setError(null);

    try {
      await onDislike();
    } catch (error) {
      console.error('Error disliking post:', error);
      const errorMessage = 'Failed to dislike post. Please try again.';
      setError(errorMessage);
      showError('Dislike Failed', errorMessage);
    } finally {
      // Use a more consistent animation duration
      setTimeout(() => setIsDislikeAnimating(false), 250);
    }
  }, [onDislike, isDislikeAnimating, isLikeAnimating, showError]);

  // Throttled handlers to prevent rapid clicking
  const handleLike = useThrottle(internalHandleLike, 500);
  const handleDislike = useThrottle(internalHandleDislike, 500);

  // Handle bookmark toggle
  const handleBookmark = useCallback(async () => {
    try {
      // Determine the correct API endpoint based on post type
      let apiUrl = '';

      if (isFanPagePost) {
        apiUrl = `/api/fan-pages/posts/${post.id}/bookmark`;
      } else if (post.type === 'blog_post') {
        // For blog posts, we need the slug instead of ID
        const blogSlug = post.blog?.slug;
        if (blogSlug) {
          apiUrl = `/api/blogs/${blogSlug}/save`;
        } else {
          showError('Bookmark Error', 'Blog slug not found');
          return;
        }
      } else {
        // Regular user posts and group posts
        apiUrl = `/api/posts/${post.id}/bookmark`;
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setIsBookmarked(data.isBookmarked || data.isSaved);
      } else {
        showError('Bookmark Error', 'Failed to update bookmark status');
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      showError('Bookmark Error', 'Failed to update bookmark status');
    }
  }, [post.id, post.type, post.blog?.slug, isFanPagePost, showError]);

  // Handle image load error
  const handleImageError = useCallback((imageSrc: string) => {
    setImageLoadError(prev => [...prev, imageSrc]);
  }, []);

  // Reset edit content when post changes or modal opens
  useEffect(() => {
    if (isEditModalOpen) {
      setEditContent(post.content || '');
      setError(null);
    }
  }, [isEditModalOpen, post.content]);

  // Check if content needs "See More" functionality
  useEffect(() => {
    const contentLength = post.content?.length || 0;
    setShowSeeMore(contentLength > 300);
  }, [post.content]);

  // Listen for comment-added events for this post
  useEffect(() => {
    const handleCommentAdded = (postId: string) => {
      if (postId === post.id) {
        setCommentCount(prev => prev + 1);
      }
    };

    eventBus.on('comment-added', handleCommentAdded);
    return () => {
      eventBus.off('comment-added', handleCommentAdded);
    };
  }, [post.id]);

  const handleEditPost = useCallback(() => {
    setEditContent(post.content || '');
    setEditImages(post.images || []);
    setIsEditModalOpen(true);
  }, [post.content, post.images]);

  const handleImageUpload = useCallback(async (files: FileList) => {
    if (!files || files.length === 0) return;

    setIsSubmitting(true);
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error('Failed to upload image');
        }

        const data = await response.json();
        return data.url;
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      setEditImages(prev => [...prev, ...uploadedUrls]);
    } catch (error) {
      console.error('Error uploading images:', error);
      setError('Failed to upload images. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  const handleRemoveImage = useCallback((index: number) => {
    setEditImages(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!editContent || !editContent.trim()) {
      setError("Post content cannot be empty");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const apiUrl = isFanPagePost
        ? `/api/fan-pages/posts/${post.id}`
        : isGroupPost
        ? `/api/groups/posts/${post.id}`
        : `/api/posts/${post.id}`;

      // Prepare the request body - only send fields that are actually needed
      // Create a clean object with only the content field initially
      const requestBody: Record<string, any> = {};

      // Always include content
      requestBody.content = editContent;

      // Always include images if they exist
      if (editImages && editImages.length > 0) {
        requestBody.images = editImages;
      }

      // For fan page posts, include additional fields if they exist and are not null/undefined
      if (isFanPagePost) {
        if (post.videos && Array.isArray(post.videos) && post.videos.length > 0) {
          requestBody.videos = post.videos;
        }
        if (post.type && post.type !== null && post.type !== undefined) {
          requestBody.type = post.type;
        }
      }

      // Always log for debugging this specific issue
      console.log('🔍 POST UPDATE DEBUG:');
      console.log('API URL:', apiUrl);
      console.log('Post type - isFanPagePost:', isFanPagePost, 'isGroupPost:', isGroupPost);
      console.log('Original post data:', {
        backgroundColor: post.backgroundColor,
        feeling: post.feeling,
        activity: post.activity,
        location: post.location,
        formattedContent: post.formattedContent
      });
      console.log('Request body being sent:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(apiUrl, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorMessage = "Failed to update post";
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
            // If there are detailed validation errors, append them
            if (errorData.errors && Array.isArray(errorData.errors)) {
              const validationDetails = errorData.errors
                .map((err: any) => `${err.field || 'Field'}: ${err.message}`)
                .join(", ");
              errorMessage += ` (${validationDetails})`;
            }
          } else if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.errors && Array.isArray(errorData.errors)) {
            // Handle validation errors
            errorMessage = errorData.errors.map((err: any) => err.message || err).join(", ");
          }
        } catch {
          // If JSON parsing fails, use status-based error message
          switch (response.status) {
            case 400:
              errorMessage = "Invalid post data. Please check your content.";
              break;
            case 401:
              errorMessage = "You are not authorized to edit this post.";
              break;
            case 403:
              errorMessage = "You don't have permission to edit this post.";
              break;
            case 404:
              errorMessage = "Post not found.";
              break;
            case 500:
              errorMessage = "Server error. Please try again later.";
              break;
            default:
              errorMessage = `Failed to update post (${response.status})`;
          }
        }
        throw new Error(errorMessage);
      }

      setIsEditModalOpen(false);
      router.refresh();
      eventBus.emit('post-updated', post.id);
    } catch (error) {
      console.error("Error updating post:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update post. Please try again.";
      setError(errorMessage);
      showError('Update Failed', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [editContent, post, isFanPagePost, isGroupPost, router, showError]);

  const handleDeletePost = useCallback(async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const apiUrl = isFanPagePost
        ? `/api/fan-pages/posts/${post.id}`
        : isGroupPost
        ? `/api/groups/posts/${post.id}`
        : `/api/posts/${post.id}`;

      const response = await fetch(apiUrl, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorMessage = "Failed to delete post";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch {
          switch (response.status) {
            case 401:
              errorMessage = "You are not authorized to delete this post.";
              break;
            case 403:
              errorMessage = "You don't have permission to delete this post.";
              break;
            case 404:
              errorMessage = "Post not found.";
              break;
            case 500:
              errorMessage = "Server error. Please try again later.";
              break;
            default:
              errorMessage = `Failed to delete post (${response.status})`;
          }
        }
        throw new Error(errorMessage);
      }

      // Emit event to remove the post from the feed
      eventBus.emit('post-deleted', { postId: post.id });

      // Show success message
      showError?.('Post deleted successfully!', 'success');

      // Refresh the page to remove the post
      router.refresh();

    } catch (error) {
      console.error('Error deleting post:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete post';
      setError(errorMessage);
      showError?.(errorMessage, 'error');
    } finally {
      setIsSubmitting(false);
    }
  }, [post, isFanPagePost, isGroupPost, router, showError]);

  return {
    // State
    showComments,
    setShowComments,
    commentCount,
    isEditModalOpen,
    setIsEditModalOpen,
    isShareModalOpen,
    setIsShareModalOpen,
    editContent,
    setEditContent,
    editImages,
    setEditImages,
    isSubmitting,
    error,
    isExpanded,
    setIsExpanded,
    showSeeMore,
    isBookmarked,
    isLikeAnimating,
    isDislikeAnimating,
    imageLoadError,
    isHovered,
    setIsHovered,
    isReactionModalOpen,
    setIsReactionModalOpen,

    // Computed values
    isCurrentUserPost,
    isFanPagePost,
    isGroupPost,
    displayInfo,

    // Handlers
    handleLike,
    handleDislike,
    handleBookmark,
    handleImageError,
    handleEditPost,
    handleSaveEdit,
    handleDeletePost,
    handleImageUpload,
    handleRemoveImage,
  };
}
